"use client";
import React, { useRef, useState, useEffect } from "react";

/* ============================== FRAMER MOTION ============================= */
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import Pagination from "../../../../../_components/Pagination";
import DataTable from "@/app/(dashboard)/project/[projectId]/analytics-traffics/_components/data-table/DataTable";
import Card from "@/components/ui/card";
import useUserDemography from "./UserDemography.hooks";
import useCountriesData from "./useCountriesData";
import NoData from "../../../../_components/NoData";

/* ========================================================================== */
const TableSection = () => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const [page, setPage] = useState(1);
  const badges = ["Countries", "Cities", "Gender", "Device", "Age"];
  const [filterBy, setFilterBy] = useState(badges ? badges[0] : "");

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  // Use countries API when "Countries" is selected, otherwise use the original hook
  const {
    data: originalData,
    isLoading: originalLoading,
    isError: originalError,
  } = useUserDemography({
    page,
    filterBy,
  });

  const {
    data: countriesData,
    isLoading: countriesLoading,
    isError: countriesError,
  } = useCountriesData({
    page,
  });

  // Determine which data to use based on the filter
  const isCountriesFilter = filterBy === "Countries";
  const data = isCountriesFilter ? countriesData : originalData;
  const isLoading = isCountriesFilter ? countriesLoading : originalLoading;
  const isError = isCountriesFilter ? countriesError : originalError;

  const lastDataRef = useRef(data);

  if (data) {
    lastDataRef.current = data;
  }

  const stableData = data ?? lastDataRef.current;

  // Reset page to 1 when filter changes
  useEffect(() => {
    setPage(1);
  }, [filterBy]);

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */

  return isError ? (
    <motion.div
      key="error"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-User's Demography" />
    </motion.div>
  ) : data?.tableData || isLoading ? (
    <Card className="w-full space-y-10 min-h-[520px] flex flex-col justify-between">
      <motion.div
        key="data"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
      >
        <DataTable
          title="All Reports-User's Demography"
          tableData={data?.tableData}
          isLoading={isLoading}
          badges={badges}
          selectedItem={filterBy}
          setSelectedItem={setFilterBy}
        />
      </motion.div>
      <Pagination
        totalPages={stableData?.pagination.totalPages || 1}
        page={page}
        onPageChange={setPage}
      />
    </Card>
  ) : (
    <motion.div
      key="nodata"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
    >
      <NoData title="All Reports-User's Demography" />
    </motion.div>
  );
};

export default TableSection;
