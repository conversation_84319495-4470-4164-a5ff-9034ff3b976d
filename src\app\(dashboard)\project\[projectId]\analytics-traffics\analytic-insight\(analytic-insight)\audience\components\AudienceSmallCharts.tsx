"use client";
import React from "react";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import LineChartCard from "../../../../overview/_components/LineChartCard";
import SmallChartSkeleton from "../../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================== TYPES ================================= */
import type { AudienceResponse, LineChartCards } from "../Audience.types";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
interface AudienceSmallChartsProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
}

/* ========================================================================== */
const AudienceSmallCharts: React.FC<AudienceSmallChartsProps> = ({
  data,
  isLoading,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartCards) => {
    // Transform the chart data to match the expected format
    const transformedData = chart.data.map((item) => ({
      name: item.name,
      value: item.value,
      // Add comparison value if available (this would come from the API response)
      comparisonValue: (item as any).comparisonValue, // Support comparison data if available
    }));

    // Check if we have comparison data
    const hasComparison = transformedData.some(
      (item) => item.comparisonValue !== undefined
    );

    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: transformedData,
      hasComparison: hasComparison,
    });
    show();
  };

  /* ========================================================================== */
  /*                              CHART COMPONENTS                             */
  /* ========================================================================== */
  const FirstChart = () => {
    const chartData = data?.lineChartCards[0];
    if (!chartData) return null;

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={chartData.data}
          bigNumber={chartData.bigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() => handleSetChartData(chartData)}
        />
      </motion.div>
    );
  };

  const SecondChart = () => {
    const chartData = data?.lineChartCards[1];
    if (!chartData) return null;

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={chartData.data}
          bigNumber={chartData.bigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() => handleSetChartData(chartData)}
        />
      </motion.div>
    );
  };

  const ThirdChart = () => {
    const chartData = data?.lineChartCards[2];
    if (!chartData) return null;

    // Calculate average by dividing bigNumber by 30
    const calculateAverageBigNumber = (bigNumber: string): string => {
      // Remove any non-numeric characters except decimal points and percentages
      const numericValue = parseFloat(bigNumber.replace(/[^\d.-]/g, ""));
      if (isNaN(numericValue)) return bigNumber;

      const average = numericValue / 30;

      // Preserve percentage sign if it exists in original
      if (bigNumber.includes("%")) {
        return `${average.toFixed(2)}%`;
      }

      // Format number with appropriate decimal places
      return average.toFixed(2);
    };

    const averagedBigNumber = calculateAverageBigNumber(chartData.bigNumber);

    return (
      <motion.div
        className="w-full"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        transition={{ duration: 0.3 }}
      >
        <LineChartCard
          data={chartData.data}
          bigNumber={averagedBigNumber}
          smallNumber={chartData.smallNumber}
          title={chartData.title}
          className="w-full cursor-pointer"
          onClick={() =>
            handleSetChartData({
              ...chartData,
              bigNumber: averagedBigNumber,
            })
          }
        />
      </motion.div>
    );
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
      {isLoading ? (
        <>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
          <motion.div
            className="w-full"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <SmallChartSkeleton className="w-full" />
          </motion.div>
        </>
      ) : (
        <>
          <FirstChart />
          <SecondChart />
          <ThirdChart />
        </>
      )}
    </div>
  );
};

export default AudienceSmallCharts;
