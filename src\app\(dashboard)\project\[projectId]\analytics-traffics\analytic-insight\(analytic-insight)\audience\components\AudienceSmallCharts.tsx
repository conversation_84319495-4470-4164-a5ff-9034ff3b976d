"use client";
import React from "react";
import { motion } from "framer-motion";

/* =============================== COMPONENTS =============================== */
import LineChartCard from "../../../../overview/_components/LineChartCard";
import SmallChartSkeleton from "../../../../_components/small-chart-skeleton/SmallChartSkeleton";

/* ================================== TYPES ================================= */
import type { AudienceResponse, LineChartCards } from "../Audience.types";

/* ================================= ZUSTAND ================================ */
import {
  useLineChartDataStore,
  useChartPopupStore,
} from "@/store/useChartPopupStore";

/* ========================================================================== */
interface AudienceSmallChartsProps {
  data: AudienceResponse | undefined;
  isLoading: boolean;
}

/* ========================================================================== */
const AudienceSmallCharts: React.FC<AudienceSmallChartsProps> = ({
  data,
  isLoading,
}) => {
  /* ========================================================================== */
  /*                                  CONSTANTS                                 */
  /* ========================================================================== */
  const setChartData = useLineChartDataStore((setData) => setData.setChartData);
  const { show } = useChartPopupStore();

  /* ========================================================================== */
  /*                                  HANDLERS                                  */
  /* ========================================================================== */
  const handleSetChartData = (chart: LineChartCards) => {
    // Transform the chart data to match the expected format
    const transformedData = chart.data.map((item) => ({
      name: item.name,
      value: item.value,
      // Add comparison value if available (this would come from the API response)
      comparisonValue: (item as any).comparisonValue, // Support comparison data if available
    }));

    // Check if we have comparison data
    const hasComparison = transformedData.some(
      (item) => item.comparisonValue !== undefined
    );

    setChartData({
      title: chart.title,
      bigNumber: chart.bigNumber,
      smallNumber: chart.smallNumber,
      data: transformedData,
      hasComparison: hasComparison,
    });
    show();
  };

  /* ========================================================================== */
  /*                                   RENDER                                   */
  /* ========================================================================== */
  return (
    <div className="flex flex-col lg:flex-row w-full mt-8 lg:gap-2 px-8 min-h-[170px]">
      {isLoading
        ? Array.from({ length: 3 }).map((_, i) => (
            <motion.div
              className="w-full"
              key={`loading-${i}`}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <SmallChartSkeleton className="w-full" />
            </motion.div>
          ))
        : data &&
          data.lineChartCards.map((item: LineChartCards, index: number) => (
            <motion.div
              key={`card-${index}`}
              className="w-full"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.3 }}
            >
              <LineChartCard
                data={item.data}
                bigNumber={item.bigNumber}
                smallNumber={item.smallNumber}
                title={item.title}
                className="w-full cursor-pointer"
                onClick={() => handleSetChartData(item)}
              />
            </motion.div>
          ))}
    </div>
  );
};

export default AudienceSmallCharts;
